/*
 *   Copyright (c) 2025
 *   All rights reserved.
 */
package it.unicam.cs.ids.piattaforma_agricola_locale.controller;

import it.unicam.cs.ids.piattaforma_agricola_locale.dto.admin.ModerationDecisionDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.dto.catalogo.ProductSummaryDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.Prodotto;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.DatiAzienda;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Venditore;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.ICuratoreService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IProdottoService;
import it.unicam.cs.ids.piattaforma_agricola_locale.dto.admin.UserStatusUpdateDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.dto.utente.UserPublicDTO;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Utente;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IUtenteService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.mapper.ProdottoMapper;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.mapper.UtenteMapper;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Controller for content moderation APIs.
 * Handles product and company approval/rejection by curators.
 */
@RestController
@RequestMapping("/api/admin")
@RequiredArgsConstructor
@Slf4j
public class AdminController {

    private final ICuratoreService curatoreService;
    private final IProdottoService prodottoService;
    private final ProdottoMapper prodottoMapper;

    // =================== PRODUCT MODERATION ===================

    @GetMapping("/products/pending")
    @PreAuthorize("hasRole('CURATORE')")
    public ResponseEntity<List<ProductSummaryDTO>> getPendingProducts(Authentication authentication) {
        List<Prodotto> prodottiInAttesa = curatoreService.getProdottiInAttesaRevisione();
        List<ProductSummaryDTO> dtos = prodottiInAttesa.stream()
                .map(prodottoMapper::toSummaryDTO)
                .collect(Collectors.toList());

        String email = authentication.getName();
        log.info("Retrieved {} products pending approval by curator: {}", dtos.size(), email);
        return ResponseEntity.ok(dtos);
    }

    @PutMapping("/products/{id}/approve")
    @PreAuthorize("hasRole('CURATORE')")
    public ResponseEntity<String> approveProduct(
            @PathVariable Long id,
            @Valid @RequestBody ModerationDecisionDTO decision,
            Authentication authentication) {

        return prodottoService.getProdottoById(id)
                .map(prodotto -> {
                    String feedbackVerifica = decision.getMotivazione() != null ? decision.getMotivazione()
                            : "Prodotto approvato";

                    curatoreService.approvaProdotto(prodotto, feedbackVerifica);

                    String email = authentication.getName();
                    log.info("Product ID: {} approved by curator: {} with feedback: {}",
                            id, email, feedbackVerifica);
                    return ResponseEntity.ok("Prodotto approvato con successo");
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/products/{id}/reject")
    @PreAuthorize("hasRole('CURATORE')")
    public ResponseEntity<String> rejectProduct(
            @PathVariable Long id,
            @Valid @RequestBody ModerationDecisionDTO decision,
            Authentication authentication) {

        return prodottoService.getProdottoById(id)
                .map(prodotto -> {
                    String feedbackVerifica = decision.getMotivazione() != null ? decision.getMotivazione()
                            : "Prodotto respinto";

                    curatoreService.respingiProdotto(prodotto, feedbackVerifica);

                    String email = authentication.getName();
                    log.warn("Product ID: {} rejected by curator: {} with feedback: {}",
                            id, email, feedbackVerifica);
                    return ResponseEntity.ok("Prodotto respinto");
                })
                .orElse(ResponseEntity.notFound().build());
    }

    // =================== COMPANY MODERATION ===================

    @GetMapping("/companies/pending")
    @PreAuthorize("hasRole('CURATORE')")
    public ResponseEntity<List<CompanyModerationDTO>> getPendingCompanies(Authentication authentication) {
        List<DatiAzienda> aziende = curatoreService.getDatiAziendaInAttesaRevisione();
        List<CompanyModerationDTO> dtos = aziende.stream()
                .map(this::mapDatiAziendaToModerationDTO)
                .collect(Collectors.toList());

        String email = authentication.getName();
        log.info("Retrieved {} companies pending approval by curator: {}", dtos.size(), email);
        return ResponseEntity.ok(dtos);
    }

    @PutMapping("/companies/{id}/approve")
    @PreAuthorize("hasRole('CURATORE')")
    public ResponseEntity<String> approveCompany(
            @PathVariable Long id,
            @Valid @RequestBody ModerationDecisionDTO decision,
            Authentication authentication) {

        // Find the vendor associated with this company data
        Venditore venditore = findVenditorByAziendaId(id);
        if (venditore == null) {
            return ResponseEntity.notFound().build();
        }

        String feedbackVerifica = decision.getMotivazione() != null ? decision.getMotivazione() : "Azienda approvata";

        curatoreService.approvaDatiAzienda(venditore, feedbackVerifica);

        String email = authentication.getName();
        log.info("Company ID: {} approved by curator: {} with feedback: {}",
                id, email, feedbackVerifica);
        return ResponseEntity.ok("Azienda approvata con successo");
    }

    @PutMapping("/companies/{id}/reject")
    @PreAuthorize("hasRole('CURATORE')")
    public ResponseEntity<String> rejectCompany(
            @PathVariable Long id,
            @Valid @RequestBody ModerationDecisionDTO decision,
            Authentication authentication) {

        // Find the vendor associated with this company data
        Venditore venditore = findVenditorByAziendaId(id);
        if (venditore == null) {
            return ResponseEntity.notFound().build();
        }

        String feedbackVerifica = decision.getMotivazione() != null ? decision.getMotivazione() : "Azienda respinta";

        curatoreService.respingiDatiAzienda(venditore, feedbackVerifica);

        String email = authentication.getName();
        log.warn("Company ID: {} rejected by curator: {} with feedback: {}",
                id, email, feedbackVerifica);
        return ResponseEntity.ok("Azienda respinta");
    }

    // =================== HELPER METHODS ===================

    private CompanyModerationDTO mapDatiAziendaToModerationDTO(DatiAzienda datiAzienda) {
        return CompanyModerationDTO.builder()
                .id(datiAzienda.getId())
                .nomeAzienda(datiAzienda.getNome())
                .partitaIva(datiAzienda.getPartitaIva())
                .indirizzo(datiAzienda.getIndirizzo())
                .telefono(datiAzienda.getTelefono())
                .email(datiAzienda.getEmail())
                .sitoWeb(datiAzienda.getSitoWeb())
                .statoVerifica(datiAzienda.getStatoVerifica().toString())
                .feedbackVerifica(datiAzienda.getFeedbackVerifica())
                .build();
    }

    private Venditore findVenditorByAziendaId(Long aziendaId) {
        // TODO: This method needs proper implementation
        // The relationship is Venditore -> DatiAzienda, not the reverse
        // Need to add a service method to find Venditore by DatiAzienda ID
        // For now, returning null to avoid compilation errors
        return null;
    }

    /**
     * DTO for company moderation information
     */
    @lombok.Data
    @lombok.Builder
    @lombok.AllArgsConstructor
    @lombok.NoArgsConstructor
    public static class CompanyModerationDTO {
        private Long id;
        private String nomeAzienda;
        private String partitaIva;
        private String indirizzo;
        private String telefono;
        private String email;
        private String sitoWeb;
        private String statoVerifica;
        private String feedbackVerifica;
    }
}