/**
 * Il package {@code interfaces} contiene le interfacce e le classi di servizio
 * che definiscono i contratti per la gestione dei pacchetti e dei prodotti
 * all'interno della piattaforma agricola locale.
 * <p>
 * Le interfacce forniscono l'astrazione delle operazioni principali offerte dai servizi,
 * mentre le classi concrete implementano la logica di business associata.
 * </p>
 * <ul>
 *   <li>{@link it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IPacchettoService} - Gestione dei pacchetti di prodotti.</li>
 *   <li>{@link it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IProdottoService} - Gestione dei prodotti offerti dai venditori.</li>
 *   <li>{@link it.unicam.cs.ids.piattaforma_agricola_locale.service.impl.PacchettoService} - Implementazione concreta delle operazioni sui pacchetti.</li>
 *   <li>{@link it.unicam.cs.ids.piattaforma_agricola_locale.service.impl.ProdottoService} - Implementazione concreta delle operazioni sui prodotti.</li>
 * </ul>
 *
 */
package it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces;
