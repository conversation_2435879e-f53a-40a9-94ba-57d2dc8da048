package it.unicam.cs.ids.piattaforma_agricola_locale.controller;

import it.unicam.cs.ids.piattaforma_agricola_locale.dto.catalogo.*;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.Certificazione;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.Prodotto;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Venditore;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.OwnershipValidationService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.ICertificazioneService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IProdottoService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IUtenteService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.mapper.ProdottoMapper;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/products")
@RequiredArgsConstructor
@Slf4j
public class ProductController {

    private final IProdottoService prodottoService;
    private final ProdottoMapper prodottoMapper;
    private final ICertificazioneService certificazioneService;
    private final IUtenteService utenteService;
    private final OwnershipValidationService ownershipValidationService;

    @GetMapping
    public ResponseEntity<Page<ProductSummaryDTO>> getAllProducts(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "nome") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDirection,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) Long vendorId) {

        Sort sort = sortDirection.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();

        Pageable pageable = PageRequest.of(page, size, sort);

        Page<Prodotto> prodotti;

        if (search != null && !search.trim().isEmpty()) {
            List<Prodotto> searchResults = prodottoService.searchProdottiByNome(search);
            prodotti = Page.empty();
            List<ProductSummaryDTO> summaryDTOs = searchResults.stream()
                    .map(prodottoMapper::toSummaryDTO)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(new PageImpl<>(summaryDTOs, pageable, searchResults.size()));
        } else if (vendorId != null) {
            List<Prodotto> vendorProducts = prodottoService.getProdottiByVenditore(vendorId);
            List<ProductSummaryDTO> summaryDTOs = vendorProducts.stream()
                    .map(prodottoMapper::toSummaryDTO)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(new PageImpl<>(summaryDTOs, pageable, vendorProducts.size()));
        } else {
            prodotti = prodottoService.getAllProdotti(pageable);
        }

        Page<ProductSummaryDTO> productSummaries = prodotti.map(prodottoMapper::toSummaryDTO);

        log.info("Retrieved {} products (page {}, size {})", productSummaries.getTotalElements(), page, size);
        return ResponseEntity.ok(productSummaries);
    }

    @GetMapping("/{id}")
    public ResponseEntity<ProductDetailDTO> getProductById(@PathVariable Long id) {
        return prodottoService.getProdottoById(id)
                .map(prodottoMapper::toDetailDTO)
                .map(productDetail -> {
                    log.info("Retrieved product details for ID: {}", id);
                    return ResponseEntity.ok(productDetail);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    public ResponseEntity<List<ProductSummaryDTO>> searchProducts(
            @RequestParam String query) {

        if (query == null || query.trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        List<Prodotto> searchResults = prodottoService.searchProdottiByNome(query);
        List<ProductSummaryDTO> summaryDTOs = searchResults.stream()
                .map(prodottoMapper::toSummaryDTO)
                .collect(Collectors.toList());

        log.info("Search for '{}' returned {} results", query, summaryDTOs.size());
        return ResponseEntity.ok(summaryDTOs);
    }

    @GetMapping("/vendor/{vendorId}")
    public ResponseEntity<List<ProductSummaryDTO>> getProductsByVendor(
            @PathVariable Long vendorId) {

        List<Prodotto> vendorProducts = prodottoService.getProdottiByVenditore(vendorId);
        List<ProductSummaryDTO> summaryDTOs = vendorProducts.stream()
                .map(prodottoMapper::toSummaryDTO)
                .collect(Collectors.toList());

        log.info("Retrieved {} products for vendor ID: {}", summaryDTOs.size(), vendorId);
        return ResponseEntity.ok(summaryDTOs);
    }

    // =================== VENDOR CRUD OPERATIONS ===================

    @PostMapping
    @PreAuthorize("hasAnyRole('PRODUTTORE', 'TRASFORMATORE')")
    public ResponseEntity<ProductDetailDTO> createProduct(
            @Valid @RequestBody CreateProductRequestDTO request,
            Authentication authentication) {

        String email = authentication.getName();
        Venditore venditore = (Venditore) utenteService.getUtenteByEmail(email);

        Prodotto nuovoProdotto = prodottoService.creaProdotto(
                request.getNome(),
                request.getDescrizione(),
                request.getPrezzo(),
                request.getQuantitaDisponibile(),
                venditore);

        ProductDetailDTO responseDTO = prodottoMapper.toDetailDTO(nuovoProdotto);
        log.info("Created new product with ID: {} by vendor: {}", nuovoProdotto.getId(), email);
        return ResponseEntity.status(HttpStatus.CREATED).body(responseDTO);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('PRODUTTORE', 'TRASFORMATORE') and @ownershipValidationService.isProductOwner(#id, authentication.name)")
    public ResponseEntity<ProductDetailDTO> updateProduct(
            @PathVariable Long id,
            @Valid @RequestBody UpdateProductRequestDTO request,
            Authentication authentication) {

        String email = authentication.getName();
        Venditore venditore = (Venditore) utenteService.getUtenteByEmail(email);

        return prodottoService.getProdottoById(id)
                .map(prodotto -> {
                    // Update non-null fields
                    if (request.getNome() != null) {
                        prodotto.setNome(request.getNome());
                    }
                    if (request.getDescrizione() != null) {
                        prodotto.setDescrizione(request.getDescrizione());
                    }
                    if (request.getPrezzo() != null) {
                        prodotto.setPrezzo(request.getPrezzo());
                    }
                    if (request.getQuantitaDisponibile() != null) {
                        prodotto.setQuantitaDisponibile(request.getQuantitaDisponibile());
                    }
                    // Note: 'attivo' field is not available in Prodotto model

                    // Note: In a real implementation, you'd save the product here
                    // For now, assuming the service handles persistence

                    ProductDetailDTO responseDTO = prodottoMapper.toDetailDTO(prodotto);
                    log.info("Updated product ID: {} by vendor: {}", id, email);
                    return ResponseEntity.ok(responseDTO);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('PRODUTTORE', 'TRASFORMATORE') and @ownershipValidationService.isProductOwner(#id, authentication.name)")
    public ResponseEntity<Void> deleteProduct(
            @PathVariable Long id,
            Authentication authentication) {

        String email = authentication.getName();
        Venditore venditore = (Venditore) utenteService.getUtenteByEmail(email);

        return prodottoService.getProdottoById(id)
                .map(prodotto -> {
                    prodottoService.rimuoviProdottoCatalogo(venditore, prodotto);
                    log.info("Deleted product ID: {} by vendor: {}", id, email);
                    return ResponseEntity.noContent().<Void>build();
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}/quantity")
    @PreAuthorize("hasAnyRole('PRODUTTORE', 'TRASFORMATORE') and @ownershipValidationService.isProductOwner(#id, authentication.name)")
    public ResponseEntity<ProductDetailDTO> updateProductQuantity(
            @PathVariable Long id,
            @Valid @RequestBody ProductQuantityUpdateDTO request,
            Authentication authentication) {

        String email = authentication.getName();
        Venditore venditore = (Venditore) utenteService.getUtenteByEmail(email);

        return prodottoService.getProdottoById(id)
                .map(prodotto -> {
                    prodottoService.aggiornaQuantitaProdotto(venditore, prodotto, request.getQuantitaDisponibile());

                    ProductDetailDTO responseDTO = prodottoMapper.toDetailDTO(prodotto);
                    log.info("Updated quantity for product ID: {} to {} by vendor: {}", id,
                            request.getQuantitaDisponibile(), email);
                    return ResponseEntity.ok(responseDTO);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    // =================== CERTIFICATION MANAGEMENT ===================

    @PostMapping("/{id}/certifications")
    @PreAuthorize("hasAnyRole('PRODUTTORE', 'TRASFORMATORE') and @ownershipValidationService.isProductOwner(#id, authentication.name)")
    public ResponseEntity<CertificazioneDTO> addCertificationToProduct(
            @PathVariable Long id,
            @Valid @RequestBody CertificazioneDTO request,
            Authentication authentication) {

        String email = authentication.getName();
        Venditore venditore = (Venditore) utenteService.getUtenteByEmail(email);

        return prodottoService.getProdottoById(id)
                .map(prodotto -> {
                    // Verify ownership
                    if (!prodotto.getVenditore().getId().equals(venditore.getId())) {
                        log.warn("Unauthorized certification addition attempt - Product ID: {}, User: {}", id, email);
                        return ResponseEntity.status(HttpStatus.FORBIDDEN).<CertificazioneDTO>build();
                    }

                    Certificazione certificazione = certificazioneService.creaCertificazionePerProdotto(
                            request.getNome(),
                            request.getEnteRilascio(),
                            request.getDataRilascio(),
                            request.getDataScadenza(),
                            prodotto);

                    CertificazioneDTO responseDTO = mapCertificazioneToDTO(certificazione);
                    log.info("Added certification {} to product ID: {} by vendor: {}", request.getNome(), id, email);
                    return ResponseEntity.status(HttpStatus.CREATED).body(responseDTO);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}/certifications/{certId}")
    @PreAuthorize("hasAnyRole('PRODUTTORE', 'TRASFORMATORE')")
    public ResponseEntity<Void> removeCertificationFromProduct(
            @PathVariable Long id,
            @PathVariable Long certId,
            Authentication authentication) {

        String email = authentication.getName();
        Venditore venditore = (Venditore) utenteService.getUtenteByEmail(email);

        return prodottoService.getProdottoById(id)
                .map(prodotto -> {
                    // Verify ownership
                    if (!prodotto.getVenditore().getId().equals(venditore.getId())) {
                        log.warn("Unauthorized certification removal attempt - Product ID: {}, User: {}", id, email);
                        return ResponseEntity.status(HttpStatus.FORBIDDEN).<Void>build();
                    }

                    prodottoService.rimuoviCertificazioneDaProdotto(prodotto, certId);
                    log.info("Removed certification ID: {} from product ID: {} by vendor: {}", certId, id, email);
                    return ResponseEntity.noContent().<Void>build();
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/{id}/certifications")
    public ResponseEntity<List<CertificazioneDTO>> getProductCertifications(@PathVariable Long id) {
        return prodottoService.getProdottoById(id)
                .map(prodotto -> {
                    List<Certificazione> certificazioni = prodottoService.getCertificazioniDelProdotto(prodotto);
                    List<CertificazioneDTO> dtos = certificazioni.stream()
                            .map(this::mapCertificazioneToDTO)
                            .collect(Collectors.toList());

                    log.info("Retrieved {} certifications for product ID: {}", dtos.size(), id);
                    return ResponseEntity.ok(dtos);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    // Helper method to map Certificazione to DTO
    private CertificazioneDTO mapCertificazioneToDTO(Certificazione certificazione) {
        return CertificazioneDTO.builder()
                .idCertificazione(certificazione.getId())
                .nomeCertificazione(certificazione.getNomeCertificazione())
                .enteRilascio(certificazione.getEnteRilascio())
                .dataRilascio(certificazione.getDataRilascio())
                .dataScadenza(certificazione.getDataScadenza())
                .build();
    }
}