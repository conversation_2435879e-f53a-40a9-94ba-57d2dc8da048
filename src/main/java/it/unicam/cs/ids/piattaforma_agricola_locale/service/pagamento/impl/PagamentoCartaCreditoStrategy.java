package it.unicam.cs.ids.piattaforma_agricola_locale.service.pagamento.impl;

import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.Ordine;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.pagamento.IMetodoPagamentoStrategy;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.pagamento.PagamentoException;
import java.util.logging.Logger;

/**
 * Implementazione della strategia di pagamento tramite carta di credito.
 * Questa classe rappresenta lo scheletro per l'elaborazione di pagamenti
 * tramite carta di credito. Al momento logga un messaggio e restituisce
 * successo.
 * 
 */
public class PagamentoCartaCreditoStrategy implements IMetodoPagamentoStrategy {

    private static final Logger logger = Logger.getLogger(PagamentoCartaCreditoStrategy.class.getName());

    /**
     * Elabora il pagamento di un ordine tramite carta di credito.
     * Al momento questa è un'implementazione scheletro che logga
     * un messaggio e restituisce sempre successo.
     * 
     * @param ordine l'ordine da elaborare per il pagamento
     * @return true se il pagamento è stato elaborato con successo
     * @throws PagamentoException       se si verifica un errore durante
     *                                  l'elaborazione del pagamento
     * @throws IllegalArgumentException se l'ordine è null
     */
    @Override
    public boolean elaboraPagamento(Ordine ordine) throws PagamentoException {
        // Validazione dell'input
        if (ordine == null) {
            throw new IllegalArgumentException("L'ordine non può essere null");
        }

        try {
            // Log dell'operazione di pagamento
            logger.info(String.format("Elaborazione pagamento carta di credito per ordine ID: %s, importo: €%.2f",
                    ordine.getIdOrdine(), ordine.getImportoTotale()));

            //  Implementare logica reale per elaborazione pagamento carta di credito
            // - Validazione dati carta
            // - Comunicazione con gateway di pagamento
            // - Gestione autorizzazione
            // - Gestione transazione

            logger.info("Pagamento carta di credito elaborato con successo");

            // Per ora restituisce sempre successo
            return true;

        } catch (Exception e) {
            logger.severe("Errore durante l'elaborazione del pagamento carta di credito: " + e.getMessage());
            throw new PagamentoException(
                    "Errore durante l'elaborazione del pagamento carta di credito: " + e.getMessage());
        }
    }
}
