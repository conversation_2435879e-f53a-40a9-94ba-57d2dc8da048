package it.unicam.cs.ids.piattaforma_agricola_locale.controller;

import it.unicam.cs.ids.piattaforma_agricola_locale.dto.catalogo.*;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.catalogo.Pacchetto;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.DistributoreDiTipicita;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IPacchettoService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IProdottoService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.interfaces.IUtenteService;
import it.unicam.cs.ids.piattaforma_agricola_locale.service.mapper.PacchettoMapper;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/packages")
@RequiredArgsConstructor
@Slf4j
public class PackageController {

    private final IPacchettoService pacchettoService;
    private final PacchettoMapper pacchettoMapper;
    private final IProdottoService prodottoService;
    private final IUtenteService utenteService;

    @GetMapping
    public ResponseEntity<Page<PacchettoSummaryDTO>> getAllPackages(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestParam(defaultValue = "nome") String sortBy,
            @RequestParam(defaultValue = "asc") String sortDirection,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) Long distributorId) {

        Sort sort = sortDirection.equalsIgnoreCase("desc") ? Sort.by(sortBy).descending() : Sort.by(sortBy).ascending();

        Pageable pageable = PageRequest.of(page, size, sort);

        Page<Pacchetto> pacchetti;

        if (search != null && !search.trim().isEmpty()) {
            List<Pacchetto> searchResults = pacchettoService.searchPacchettiByNome(search);
            List<PacchettoSummaryDTO> summaryDTOs = searchResults.stream()
                    .map(pacchettoMapper::toSummaryDTO)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(new PageImpl<>(summaryDTOs, pageable, searchResults.size()));
        } else if (distributorId != null) {
            List<Pacchetto> distributorPackages = pacchettoService.getPacchettiByDistributore(distributorId);
            List<PacchettoSummaryDTO> summaryDTOs = distributorPackages.stream()
                    .map(pacchettoMapper::toSummaryDTO)
                    .collect(Collectors.toList());

            return ResponseEntity.ok(new PageImpl<>(summaryDTOs, pageable, distributorPackages.size()));
        } else {
            pacchetti = pacchettoService.getAllPacchetti(pageable);
        }

        Page<PacchettoSummaryDTO> packageSummaries = pacchetti.map(pacchettoMapper::toSummaryDTO);

        log.info("Retrieved {} packages (page {}, size {})", packageSummaries.getTotalElements(), page, size);
        return ResponseEntity.ok(packageSummaries);
    }

    @GetMapping("/{id}")
    public ResponseEntity<PacchettoDetailDTO> getPackageById(@PathVariable Long id) {
        return pacchettoService.getPacchettoById(id)
                .map(pacchettoMapper::toDetailDTO)
                .map(packageDetail -> {
                    log.info("Retrieved package details for ID: {}", id);
                    return ResponseEntity.ok(packageDetail);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/search")
    public ResponseEntity<List<PacchettoSummaryDTO>> searchPackages(
            @RequestParam String query) {

        if (query == null || query.trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        List<Pacchetto> searchResults = pacchettoService.searchPacchettiByNome(query);
        List<PacchettoSummaryDTO> summaryDTOs = searchResults.stream()
                .map(pacchettoMapper::toSummaryDTO)
                .collect(Collectors.toList());

        log.info("Search for '{}' returned {} results", query, summaryDTOs.size());
        return ResponseEntity.ok(summaryDTOs);
    }

    @GetMapping("/distributor/{distributorId}")
    public ResponseEntity<List<PacchettoSummaryDTO>> getPackagesByDistributor(
            @PathVariable Long distributorId) {

        List<Pacchetto> distributorPackages = pacchettoService.getPacchettiByDistributore(distributorId);
        List<PacchettoSummaryDTO> summaryDTOs = distributorPackages.stream()
                .map(pacchettoMapper::toSummaryDTO)
                .collect(Collectors.toList());

        log.info("Retrieved {} packages for distributor ID: {}", summaryDTOs.size(), distributorId);
        return ResponseEntity.ok(summaryDTOs);
    }

    // =================== DISTRIBUTOR CRUD OPERATIONS ===================

    @PostMapping
    @PreAuthorize("hasRole('DISTRIBUTORE_DI_TIPICITA')")
    public ResponseEntity<PacchettoDetailDTO> createPackage(
            @Valid @RequestBody CreatePacchettoRequestDTO request,
            Authentication authentication) {

        String email = authentication.getName();
        DistributoreDiTipicita distributore = (DistributoreDiTipicita) utenteService.getUtenteByEmail(email);

        pacchettoService.creaPacchetto(
                distributore,
                request.getNome(),
                request.getDescrizione(),
                request.getQuantitaDisponibile(),
                request.getPrezzoTotale());

        // Note: Service doesn't return the created package, so we'll fetch by name
        // This is a limitation of the current service interface
        List<Pacchetto> distributorPackages = pacchettoService.getPacchettiByDistributore(distributore.getId());
        Pacchetto nuovoPacchetto = distributorPackages.stream()
                .filter(p -> p.getNome().equals(request.getNome()))
                .findFirst()
                .orElseThrow(() -> new RuntimeException("Package creation failed"));

        PacchettoDetailDTO responseDTO = pacchettoMapper.toDetailDTO(nuovoPacchetto);
        log.info("Created new package with ID: {} by distributor: {}", nuovoPacchetto.getId(), email);
        return ResponseEntity.status(HttpStatus.CREATED).body(responseDTO);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasRole('DISTRIBUTORE_DI_TIPICITA')")
    public ResponseEntity<PacchettoDetailDTO> updatePackage(
            @PathVariable Long id,
            @Valid @RequestBody UpdatePacchettoRequestDTO request,
            Authentication authentication) {

        String email = authentication.getName();
        DistributoreDiTipicita distributore = (DistributoreDiTipicita) utenteService.getUtenteByEmail(email);

        return pacchettoService.getPacchettoById(id)
                .map(pacchetto -> {
                    // Verify ownership
                    if (!pacchetto.getDistributore().getId().equals(distributore.getId())) {
                        log.warn("Unauthorized package update attempt - Package ID: {}, User: {}", id, email);
                        return ResponseEntity.status(HttpStatus.FORBIDDEN).<PacchettoDetailDTO>build();
                    }

                    // Update non-null fields
                    if (request.getNome() != null) {
                        pacchetto.setNome(request.getNome());
                    }
                    if (request.getDescrizione() != null) {
                        pacchetto.setDescrizione(request.getDescrizione());
                    }
                    if (request.getPrezzoTotale() != null) {
                        pacchetto.setPrezzoTotale(request.getPrezzoTotale());
                    }
                    if (request.getQuantitaDisponibile() != null) {
                        pacchetto.setQuantitaDisponibile(request.getQuantitaDisponibile());
                    }
                    // Note: 'attivo' field is not available in Pacchetto model

                    PacchettoDetailDTO responseDTO = pacchettoMapper.toDetailDTO(pacchetto);
                    log.info("Updated package ID: {} by distributor: {}", id, email);
                    return ResponseEntity.ok(responseDTO);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('DISTRIBUTORE_DI_TIPICITA')")
    public ResponseEntity<Void> deletePackage(
            @PathVariable Long id,
            Authentication authentication) {

        String email = authentication.getName();
        DistributoreDiTipicita distributore = (DistributoreDiTipicita) utenteService.getUtenteByEmail(email);

        return pacchettoService.getPacchettoById(id)
                .map(pacchetto -> {
                    // Verify ownership
                    if (!pacchetto.getDistributore().getId().equals(distributore.getId())) {
                        log.warn("Unauthorized package deletion attempt - Package ID: {}, User: {}", id, email);
                        return ResponseEntity.status(HttpStatus.FORBIDDEN).<Void>build();
                    }

                    pacchettoService.rimuoviPacchettoCatalogo(distributore, pacchetto);
                    log.info("Deleted package ID: {} by distributor: {}", id, email);
                    return ResponseEntity.noContent().<Void>build();
                })
                .orElse(ResponseEntity.notFound().build());
    }

    // =================== PACKAGE PRODUCT MANAGEMENT ===================

    @PostMapping("/{id}/products")
    @PreAuthorize("hasRole('DISTRIBUTORE_DI_TIPICITA')")
    public ResponseEntity<PacchettoDetailDTO> addProductToPackage(
            @PathVariable Long id,
            @Valid @RequestBody ElementoPacchettoRequestDTO request,
            Authentication authentication) {

        String email = authentication.getName();
        DistributoreDiTipicita distributore = (DistributoreDiTipicita) utenteService.getUtenteByEmail(email);

        return pacchettoService.getPacchettoById(id)
                .map(pacchetto -> {
                    // Verify ownership
                    if (!pacchetto.getDistributore().getId().equals(distributore.getId())) {
                        log.warn("Unauthorized product addition attempt - Package ID: {}, User: {}", id, email);
                        return ResponseEntity.status(HttpStatus.FORBIDDEN).<PacchettoDetailDTO>build();
                    }

                    return prodottoService.getProdottoById(request.getIdProdotto())
                            .map(prodotto -> {
                                pacchettoService.aggiungiProdottoAlPacchetto(distributore, pacchetto, prodotto);

                                PacchettoDetailDTO responseDTO = pacchettoMapper.toDetailDTO(pacchetto);
                                log.info("Added product ID: {} to package ID: {} by distributor: {}",
                                        request.getIdProdotto(), id, email);
                                return ResponseEntity.ok(responseDTO);
                            })
                            .orElse(ResponseEntity.badRequest().<PacchettoDetailDTO>build());
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @DeleteMapping("/{id}/products/{productId}")
    @PreAuthorize("hasRole('DISTRIBUTORE_DI_TIPICITA')")
    public ResponseEntity<PacchettoDetailDTO> removeProductFromPackage(
            @PathVariable Long id,
            @PathVariable Long productId,
            Authentication authentication) {

        String email = authentication.getName();
        DistributoreDiTipicita distributore = (DistributoreDiTipicita) utenteService.getUtenteByEmail(email);

        return pacchettoService.getPacchettoById(id)
                .map(pacchetto -> {
                    // Verify ownership
                    if (!pacchetto.getDistributore().getId().equals(distributore.getId())) {
                        log.warn("Unauthorized product removal attempt - Package ID: {}, User: {}", id, email);
                        return ResponseEntity.status(HttpStatus.FORBIDDEN).<PacchettoDetailDTO>build();
                    }

                    return prodottoService.getProdottoById(productId)
                            .map(prodotto -> {
                                pacchettoService.rimuoviProdottoDalPacchetto(distributore, pacchetto, prodotto);

                                PacchettoDetailDTO responseDTO = pacchettoMapper.toDetailDTO(pacchetto);
                                log.info("Removed product ID: {} from package ID: {} by distributor: {}",
                                        productId, id, email);
                                return ResponseEntity.ok(responseDTO);
                            })
                            .orElse(ResponseEntity.badRequest().<PacchettoDetailDTO>build());
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @PutMapping("/{id}/pricing")
    @PreAuthorize("hasRole('DISTRIBUTORE_DI_TIPICITA')")
    public ResponseEntity<PacchettoDetailDTO> updatePackagePricing(
            @PathVariable Long id,
            @Valid @RequestBody UpdatePacchettoRequestDTO request,
            Authentication authentication) {

        String email = authentication.getName();
        DistributoreDiTipicita distributore = (DistributoreDiTipicita) utenteService.getUtenteByEmail(email);

        return pacchettoService.getPacchettoById(id)
                .map(pacchetto -> {
                    // Verify ownership
                    if (!pacchetto.getDistributore().getId().equals(distributore.getId())) {
                        log.warn("Unauthorized pricing update attempt - Package ID: {}, User: {}", id, email);
                        return ResponseEntity.status(HttpStatus.FORBIDDEN).<PacchettoDetailDTO>build();
                    }

                    if (request.getPrezzoTotale() != null) {
                        pacchetto.setPrezzoTotale(request.getPrezzoTotale());
                    }

                    PacchettoDetailDTO responseDTO = pacchettoMapper.toDetailDTO(pacchetto);
                    log.info("Updated pricing for package ID: {} to {} by distributor: {}",
                            id, request.getPrezzoTotale(), email);
                    return ResponseEntity.ok(responseDTO);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping("/{id}/composition")
    public ResponseEntity<PackageCompositionDTO> getPackageComposition(@PathVariable Long id) {
        return pacchettoService.getPacchettoById(id)
                .map(pacchetto -> {
                    PackageCompositionDTO composition = PackageCompositionDTO.builder()
                            .id(pacchetto.getId())
                            .nome(pacchetto.getNome())
                            .descrizione(pacchetto.getDescrizione())
                            .prezzoTotale(pacchetto.getPrezzoTotale())
                            .quantitaDisponibile(pacchetto.getQuantitaDisponibile())
                            .elementi(pacchetto.getElementi().stream()
                                    .map(elemento -> ElementoPacchettoDTO.builder()
                                            .tipoElemento(elemento.getClass().getSimpleName().toUpperCase())
                                            .idElemento(elemento.getId())
                                            .nomeElemento(elemento.getNome())
                                            .descrizioneElemento(elemento.getDescrizione())
                                            .prezzoElemento(elemento.getPrezzo())
                                            .build())
                                    .collect(Collectors.toList()))
                            .prezzoCalcolato(calculatePackagePrice(pacchetto))
                            .disponibilitaCompleta(checkPackageAvailability(pacchetto))
                            .build();

                    log.info("Retrieved composition for package ID: {}", id);
                    return ResponseEntity.ok(composition);
                })
                .orElse(ResponseEntity.notFound().build());
    }

    // Helper methods
    private Double calculatePackagePrice(Pacchetto pacchetto) {
        // For now, sum the individual prices of all elements
        // TODO: Implement proper quantity-based calculation when element quantity logic
        // is clarified
        return pacchetto.getElementi().stream()
                .mapToDouble(elemento -> elemento.getPrezzo())
                .sum();
    }

    private Boolean checkPackageAvailability(Pacchetto pacchetto) {
        // For now, always return true since individual element availability
        // checking requires quantity information not available in Acquistabile
        // interface
        // TODO: Implement proper availability checking when element quantity logic is
        // clarified
        return true;
    }
}