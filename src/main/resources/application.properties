# Database configuration (H2 in-memory for development)
spring.datasource.url=jdbc:h2:mem:piattaforma_agricola
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=password

# H2 Console (for development/testing)
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console

# JPA/Hibernate configuration
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Logging
logging.level.it.unicam.cs.ids.piattaforma_agricola_locale=DEBUG
logging.level.org.springframework.web=DEBUG
logging.level.org.hibernate.SQL=DEBUG

# Application properties
server.port=8080
spring.application.name=piattaforma-agricola-locale


#AUTENTICAZIONE

# Chiave Segreta JWT, generata casualmente
jwt.secret.key=4D6251655468576D5A7134743777217A25432A462D4A614E645267556B587032

# Durata di scadenza dei token in millisecondi
# Access Token: 8 ore (28800000 ms)
jwt.expiration.access-token=28800000
